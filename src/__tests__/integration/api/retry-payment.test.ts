/**
 * Retry Payment API 路由測試
 * 測試 /api/retry-payment 端點的完整功能
 */

import { NextRequest } from 'next/server';
import { POST } from '@/app/api/retry-payment/route';

// Mock dependencies
jest.mock('@/lib/google-sheets', () => ({
  getSheetData: jest.fn(),
  updateSheetRow: jest.fn(),
}));

jest.mock('@/lib/payuni', () => ({
  createPaymentRequest: jest.fn(),
  calculateATMExpireDate: jest.fn(),
}));

jest.mock('@/config/environment-config', () => ({
  PAYUNI_CONFIG: {
    getMerchantId: jest.fn(() => 'S01421169'),
  },
}));

// Mock console methods to reduce test noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;

beforeAll(() => {
  console.log = jest.fn();
  console.error = jest.fn();
});

afterAll(() => {
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
});

describe('/api/retry-payment', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock environment variables
    process.env.APP_ENVIRONMENT = 'sandbox';
    process.env.NEXT_PUBLIC_BASE_URL = 'http://localhost:3000';
  });

  const mockGoogleSheetsData = [
    // Header row
    ['姓名', '電子郵件', '活動名稱', '活動價格', '...', '訂單號碼', '付款狀態'],
    // Data rows
    ['張三', '<EMAIL>', '錶匠體驗機芯拆解', '1500', '...', 'pangea_1234567890', '未付款'],
    ['李四', '<EMAIL>', '錶匠體驗機芯拆解', '1500', '...', 'pangea_1234567891', '已付款'],
  ];

  const mockPaymentResponse = {
    MerID: 'S01421169',
    Version: '1.0',
    EncryptInfo: 'encrypted_data_here',
    HashInfo: 'hash_info_here',
    paymentUrl: 'https://sandbox-api.payuni.com.tw/api/payment',
  };

  describe('成功案例', () => {
    test('應該成功處理重新付款請求', async () => {
      const { getSheetData, updateSheetRow } = require('@/lib/google-sheets');
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);
      updateSheetRow.mockResolvedValue(true);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.newOrderNo).toMatch(/^pangea_retry_\d+$/);
      expect(data.MerID).toBe('S01421169');
      expect(data.EncryptInfo).toBe('encrypted_data_here');
      expect(data.HashInfo).toBe('hash_info_here');
      expect(data.paymentUrl).toBe('https://sandbox-api.payuni.com.tw/api/payment');
      
      // 檢查是否更新了 Google Sheets
      expect(updateSheetRow).toHaveBeenCalled();
    });

    test('應該生成唯一的重新付款訂單號碼', async () => {
      const { getSheetData, updateSheetRow } = require('@/lib/google-sheets');
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);
      updateSheetRow.mockResolvedValue(true);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      // 第一次重新付款
      const request1 = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response1 = await POST(request1);
      const data1 = await response1.json();

      // 第二次重新付款
      const request2 = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response2 = await POST(request2);
      const data2 = await response2.json();

      expect(data1.newOrderNo).not.toBe(data2.newOrderNo);
      expect(data1.newOrderNo).toMatch(/^pangea_retry_\d+$/);
      expect(data2.newOrderNo).toMatch(/^pangea_retry_\d+$/);
    });

    test('應該正確更新 Google Sheets 中的重新付款訂單號碼', async () => {
      const { getSheetData, updateSheetRow } = require('@/lib/google-sheets');
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);
      updateSheetRow.mockResolvedValue(true);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(updateSheetRow).toHaveBeenCalledWith(
        expect.any(String), // spreadsheetId
        expect.any(String), // worksheetName
        2, // rowIndex (第二行，因為第一行是標題)
        expect.any(Number), // columnIndex (AB 欄位)
        data.newOrderNo
      );
    });

    test('應該使用原始訂單的資料建立新付款', async () => {
      const { getSheetData, updateSheetRow } = require('@/lib/google-sheets');
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);
      updateSheetRow.mockResolvedValue(true);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      await POST(request);

      const createPaymentCall = createPaymentRequest.mock.calls[0][0];
      expect(createPaymentCall.TradeAmt).toBe(1500);
      expect(createPaymentCall.ProdDesc).toBe('錶匠體驗機芯拆解');
      expect(createPaymentCall.UsrMail).toBe('<EMAIL>');
      expect(createPaymentCall.MerTradeNo).toMatch(/^pangea_retry_\d+$/);
    });
  });

  describe('驗證錯誤', () => {
    test('應該拒絕空的訂單號碼', async () => {
      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: '' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('請提供訂單號碼');
    });

    test('應該拒絕缺少訂單號碼的請求', async () => {
      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({}),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('請提供訂單號碼');
    });

    test('應該拒絕已付款的訂單', async () => {
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567891' }), // 已付款的訂單
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toContain('此訂單已完成付款');
    });

    test('應該拒絕找不到的訂單', async () => {
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'nonexistent_order' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(404);
      expect(data.error).toContain('找不到指定的訂單');
    });
  });

  describe('錯誤處理', () => {
    test('應該處理 Google Sheets 查詢失敗', async () => {
      const { getSheetData } = require('@/lib/google-sheets');
      
      getSheetData.mockRejectedValue(new Error('Google Sheets API 錯誤'));

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('重新付款失敗');
    });

    test('應該處理 PayUni 創建付款失敗', async () => {
      const { getSheetData, updateSheetRow } = require('@/lib/google-sheets');
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);
      updateSheetRow.mockResolvedValue(true);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockImplementation(() => {
        throw new Error('PayUni API 錯誤');
      });

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('重新付款失敗');
    });

    test('應該處理 Google Sheets 更新失敗', async () => {
      const { getSheetData, updateSheetRow } = require('@/lib/google-sheets');
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);
      updateSheetRow.mockRejectedValue(new Error('更新失敗'));
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('重新付款失敗');
    });

    test('應該處理無效的 JSON 請求', async () => {
      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: 'invalid json',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.error).toContain('重新付款失敗');
    });
  });

  describe('訂單號碼格式', () => {
    test('應該正確處理原始訂單號碼', async () => {
      const { getSheetData, updateSheetRow } = require('@/lib/google-sheets');
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      getSheetData.mockResolvedValue(mockGoogleSheetsData);
      updateSheetRow.mockResolvedValue(true);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(data.newOrderNo).toMatch(/^pangea_retry_\d+$/);
      expect(data.newOrderNo).not.toBe('pangea_1234567890');
    });

    test('應該正確處理已經是重新付款的訂單號碼', async () => {
      const retryOrderData = [
        ['姓名', '電子郵件', '活動名稱', '活動價格', '...', '訂單號碼', '付款狀態'],
        ['張三', '<EMAIL>', '錶匠體驗機芯拆解', '1500', '...', 'pangea_retry_1234567890', '未付款'],
      ];

      const { getSheetData, updateSheetRow } = require('@/lib/google-sheets');
      const { createPaymentRequest, calculateATMExpireDate } = require('@/lib/payuni');
      
      getSheetData.mockResolvedValue(retryOrderData);
      updateSheetRow.mockResolvedValue(true);
      calculateATMExpireDate.mockReturnValue('2024-07-22');
      createPaymentRequest.mockReturnValue(mockPaymentResponse);

      const request = new NextRequest('http://localhost:3000/api/retry-payment', {
        method: 'POST',
        body: JSON.stringify({ orderNo: 'pangea_retry_1234567890' }),
        headers: {
          'Content-Type': 'application/json',
        },
      });

      const response = await POST(request);
      const data = await response.json();

      expect(data.newOrderNo).toMatch(/^pangea_retry_\d+$/);
      expect(data.newOrderNo).not.toBe('pangea_retry_1234567890');
    });
  });
});
